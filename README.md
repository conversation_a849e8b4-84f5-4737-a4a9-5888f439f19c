# czscflow

基于 czsc 库的量化研究流程开发应用；多端支持；ALL IN ONE 设计；量化投研、策略监控、实盘交易全流程管理

框架基于 Vue3 + ts + vite + antdv + tauri，前端应用中有热更新，更新后可自动更新页面。

## 安装依赖

`pnpm install`

## 桌面应用启动

`pnpm tauri dev`

`npm run tauri dev -- --release` 前端使用这个, 减少target目录的体积

## 桌面应用构建

先执行：

`pnpm build`

再执行：

`pnpm tauri build`

安装文件目录：

`apps\web-antd\src-tauri\target`

## 重新构建环境

```bash
git clone --recurse-submodules **************:zengbin93/czscflow.git
rustup override set 1.85.0
pnpm install
pnpm tauri dev
```
