{"page": {"core": {"login": "<PERSON><PERSON>", "register": "Register", "codeLogin": "Code Login", "qrcodeLogin": "Qr Code Login", "forgetPassword": "Forget Password"}, "dashboard": {"title": "Dashboard", "analytics": "Analytics", "workspace": "Workspace"}, "vben": {"title": "Project", "about": "About", "document": "Document", "antdv": "Ant Design Vue Version", "naive-ui": "Naive UI Version", "element-plus": "Element Plus Version"}}, "common": {"back": "Back", "backToHome": "Back To Home", "login": "<PERSON><PERSON>", "logout": "Logout", "prompt": "Prompt", "cancel": "Cancel", "confirm": "Comfirm", "noData": "No Data", "refresh": "Refresh", "loadingMenu": "Loading Menu"}, "fallback": {"pageNotFound": "Oops! Page Not Found", "pageNotFoundDesc": "Sorry, we couldn't find the page you were looking for.", "forbidden": "Oops! Access Denied", "forbiddenDesc": "Sorry, but you don't have permission to access this page.", "internalError": "Oops! Something Went Wrong", "internalErrorDesc": "Sorry, but the server encountered an error.", "offline": "Offline Page", "offlineError": "Oops! Network Error", "offlineErrorDesc": "Sorry, can't connect to the internet. Check your connection.", "comingSoon": "Coming Soon", "http": {"requestTimeout": "The request timed out. Please try again later.", "networkError": "A network error occurred. Please check your internet connection and try again.", "badRequest": "Bad Request. Please check your input and try again.", "unauthorized": "Unauthorized. Please log in to continue.", "forbidden": "Forbidden. You do not have permission to access this resource.", "notFound": "Not Found. The requested resource could not be found.", "internalServerError": "Internal Server Error. Something went wrong on our end. Please try again later."}}, "formRules": {"required": "Please enter {0}", "selectRequired": "Please select {0}"}, "placeholder": {"input": "Please enter", "select": "Please select"}, "widgets": {"document": "Document", "qa": "Q&A", "setting": "Settings", "logoutTip": "Do you want to logout?", "viewAll": "View All Messages", "notifications": "Notifications", "markAllAsRead": "Make All as Read", "clearNotifications": "Clear", "checkUpdatesTitle": "New Version Available", "checkUpdatesDescription": "Click to refresh and get the latest version", "search": {"title": "Search", "searchNavigate": "Search Navigation", "select": "Select", "navigate": "Navigate", "close": "Close", "noResults": "No Search Results Found", "noRecent": "No Search History", "recent": "Search History"}, "lockScreen": {"title": "Lock Screen", "screenButton": "Locking", "password": "Password", "placeholder": "Please enter password", "unlock": "Click to unlock", "errorPasswordTip": "Password error, please re-enter", "backToLogin": "Back to login", "entry": "Enter the system"}}, "authentication": {"welcomeBack": "Welcome Back", "pageTitle": "Plug-and-play Admin system", "pageDesc": "Efficient, versatile frontend template", "loginSuccess": "Login Successful", "loginSuccessDesc": "Welcome Back", "loginSubtitle": "Enter your account details to manage your projects", "selectAccount": "Quick Select Account", "username": "Username", "password": "Password", "usernameTip": "Please enter username", "passwordErrorTip": "Password is incorrect", "passwordTip": "Please enter password", "verifyRequiredTip": "Please complete the verification first", "rememberMe": "Remember Me", "createAnAccount": "Create an Account", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "accountTip": "Don't have an account?", "signUp": "Sign Up", "signUpSubtitle": "Make managing your applications simple and fun", "confirmPassword": "Comfirm Password", "confirmPasswordTip": "The passwords do not match", "agree": "I agree to", "privacyPolicy": "Privacy-policy", "terms": "Terms", "agreeTip": "Please agree to the Privacy Policy and Terms", "goToLogin": "<PERSON><PERSON> instead", "passwordStrength": "Use 8 or more characters with a mix of letters, numbers & symbols", "forgetPassword": "Forget Password?", "forgetPasswordSubtitle": "Enter your email and we'll send you instructions to reset your password", "emailTip": "Please enter email", "emailValidErrorTip": "The email format you entered is incorrect", "sendResetLink": "Send Reset Link", "email": "Email", "qrcodeSubtitle": "Scan the QR code with your phone to login", "qrcodePrompt": "Click 'Confirm' after scanning to complete login", "qrcodeLogin": "QR Code Login", "codeSubtitle": "Enter your phone number to start managing your project", "code": "Security code", "codeTip": "Security code is required", "mobile": "Mobile", "mobileLogin": "Mobile Login", "mobileTip": "Please enter mobile number", "mobileErrortip": "The phone number format is incorrect", "sendCode": "Get Security code", "sendText": "Resend in {0}s", "thirdPartyLogin": "Or continue with", "loginAgainTitle": "Please Log In Again", "loginAgainSubTitle": "Your login session has expired. Please log in again to continue.", "layout": {"center": "Align Center", "alignLeft": "Align Left", "alignRight": "Align Right"}}, "preferences": {"title": "Preferences", "subtitle": "Customize Preferences & Preview in Real Time", "resetTip": "Data has changed, click to reset", "resetTitle": "Reset Preferences", "resetSuccess": "Preferences reset successfully", "appearance": "Appearance", "layout": "Layout", "content": "Content", "other": "Other", "wide": "Wide", "compact": "Fixed", "followSystem": "Follow System", "vertical": "Vertical", "verticalTip": "Side vertical menu mode", "horizontal": "Horizontal", "horizontalTip": "Horizontal menu mode, all menus displayed at the top", "twoColumn": "Two Column", "twoColumnTip": "Vertical Two Column Menu Mode", "mixedMenu": "Mixed Menu", "mixedMenuTip": "Vertical & Horizontal Menu Co-exists", "fullContent": "Full Content", "fullContentTip": "Only display content body, hide all menus", "normal": "Normal", "plain": "Plain", "rounded": "Rounded", "copyPreferences": "Copy Preferences", "copyPreferencesSuccess": "Copy successful, please override in `src/preferences.ts` under app", "clearAndLogout": "Clear Cache & Logout", "mode": "Mode", "general": "General", "language": "Language", "dynamicTitle": "Dynamic Title", "watermark": "Watermark", "checkUpdates": "Periodic update check", "position": {"title": "Preferences Postion", "header": "Header", "auto": "Auto", "fixed": "Fixed"}, "sidebar": {"title": "Sidebar", "width": "<PERSON><PERSON><PERSON>", "visible": "Show Sidebar", "collapsed": "Collpase <PERSON>u", "collapsedShowTitle": "Show Menu Title"}, "tabbar": {"title": "Ta<PERSON><PERSON>", "enable": "Enable Tab Bar", "icon": "Show Tabbar Icon", "showMore": "Show More Button", "showRefresh": "Show Refresh <PERSON>", "showMaximize": "Show Maximize <PERSON>ton", "persist": "Persist Tabs", "dragable": "Enable Dragable Sort", "styleType": {"title": "Tabs Style", "chrome": "Chrome", "card": "Card", "plain": "Plain", "brisk": "Brisk"}, "contextMenu": {"reload": "Reload", "close": "Close", "pin": "<PERSON>n", "unpin": "Unpin", "closeLeft": "Close Left Tabs", "closeRight": "Close Right Tabs", "closeOther": "Close Other Tabs", "closeAll": "Close All Tabs", "openInNewWindow": "Open in New Window", "maximize": "Maximize", "restoreMaximize": "Rest<PERSON>"}}, "navigationMenu": {"title": "Navigation Menu", "style": "Navigation Menu Style", "accordion": "<PERSON>bar Accordion <PERSON>u", "split": "Navigation Menu Separation", "splitTip": "When enabled, the sidebar displays the top bar's submenu"}, "breadcrumb": {"title": "Breadcrumb", "home": "Show Home Button", "enable": "Enable Breadcrumb", "icon": "Show Breadcrumb Icon", "background": "background", "style": "Breadcrumb Style", "hideOnlyOne": "Hidden when only one"}, "animation": {"title": "Animation", "loading": "Page Loading", "transition": "Page Transition", "progress": "Page Progress"}, "theme": {"title": "Theme", "radius": "<PERSON><PERSON>", "light": "Light", "dark": "Dark", "darkSidebar": "Semi Dark Sidebar", "darkHeader": "Semi Dark Header", "weakMode": "Weak Mode", "grayMode": "Gray Mode", "builtin": {"title": "Built-in", "default": "<PERSON><PERSON><PERSON>", "violet": "Violet", "pink": "Pink", "rose": "<PERSON>", "skyBlue": "Sky Blue", "deepBlue": "Deep Blue", "green": "Green", "deepGreen": "Deep Green", "orange": "Orange", "yellow": "Yellow", "zinc": "Zinc", "neutral": "Neutral", "slate": "Slate", "gray": "<PERSON>", "custom": "Custom"}}, "header": {"title": "Header", "visible": "Show Header", "modeStatic": "Static", "modeFixed": "Fixed", "modeAuto": "Auto hide & Show", "modeAutoScroll": "Scroll to Hide & Show"}, "footer": {"title": "Footer", "visible": "Show Footer", "fixed": "Fixed at Bottom"}, "copyright": {"title": "Copyright", "enable": "Enable Copyright", "companyName": "Company Name", "companySiteLink": "Company Site Link", "date": "Date", "icp": "ICP License Number", "icpLink": "ICP Site Link"}, "shortcutKeys": {"title": "Shortcut Keys", "global": "Global", "search": "Global Search", "logout": "Logout", "preferences": "Preferences"}, "widget": {"title": "Widget", "globalSearch": "Enable Global Search", "fullscreen": "Enable Fullscreen", "themeToggle": "Enable Theme Toggle", "languageToggle": "Enable Language Toggle", "notification": "Enable Notification", "sidebarToggle": "Enable Sidebar Toggle", "lockScreen": "Enable Lock Screen"}}, "ui": {"captcha": {"title": "Please complete the security verification", "sliderSuccessText": "Passed", "sliderDefaultText": "Slider and drag", "alt": "Supports img tag src attribute value", "sliderRotateDefaultTip": "Click picture to refresh", "sliderRotateFailTip": "Validation failed", "sliderRotateSuccessTip": "Validation successful, time {0} seconds", "refreshAriaLabel": "Refresh cap<PERSON>a", "confirmAriaLabel": "Confirm selection", "confirm": "Confirm", "pointAriaLabel": "Click point", "clickInOrder": "Please click in order"}}}